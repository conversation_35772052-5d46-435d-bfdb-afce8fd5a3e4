import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class H2O {
    private int hCount = 0;
    private int oCount = 0;
    public int moleculeCount = 0;
    private Lock lock = new ReentrantLock();
    private Condition hCondition = lock.newCondition();
    private Condition oCondition = lock.newCondition();

    public H2O() {
    }

    public void hydrogen() throws InterruptedException {
        lock.lock();
        try {
            while (hCount == 2) {
                if (oCount == 0) {
                    hCondition.await();
                }
                oCondition.signalAll();
                moleculeCount++;
                hCount = 0;
                oCount = 0;
            }
            hCount++;
        } finally {
            lock.unlock();
        }
    }

    public void oxygen() throws InterruptedException {
        lock.lock();
        try {
            while (oCount == 1) {
                if (hCount < 2) {
                    oCondition.await();
                }
                hCondition.signalAll();
                moleculeCount++;
                hCount = 0;
                oCount = 0;
            }
            oCount++;
        } finally {
            lock.unlock();
        }
    }
}
