import java.util.ArrayList;
import java.util.List;

public class Main {
    public static void main(String[] args) {
        H2O h2o = new H2O();
        List<Thread> hydrogenThreads = new ArrayList<>();
        List<Thread> oxygenThreads = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            hydrogenThreads.add(new HydroThread(h2o));
            oxygenThreads.add(new OxygenThread(h2o));
        }

        hydrogenThreads.forEach(Thread::start);
        oxygenThreads.forEach(Thread::start);

        try {
            hydrogenThreads.forEach(thread -> {
                try {
                    thread.join();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
            oxygenThreads.forEach(thread -> {
                try {
                    thread.join();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            });
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        

        System.out.println(h2o.moleculeCount);
    }
}

class HydroThread extends Thread {
    private H2O h2o;

    public HydroThread(H2O h2o) {
        this.h2o = h2o;
    }

    @Override
    public void run() {
        try {
            h2o.hydrogen();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}

class OxygenThread extends Thread {
    private H2O h2o;

    public OxygenThread(H2O h2o) {
        this.h2o = h2o;
    }

    @Override
    public void run() {
        try {
            h2o.oxygen();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
